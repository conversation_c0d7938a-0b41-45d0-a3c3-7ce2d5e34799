# WeChat Mini Program Compatibility Troubleshooting

## 🚨 Error: `TypeError: l.getStaticSystemInfoSync is not a function`

This error occurs when there's a compatibility issue between the uniapp framework and the WeChat Mini Program base library version.

## 🔧 Solution Steps

### Step 1: Update Configuration Files

The following files have been updated:

1. **manifest.json** - Added proper WeChat Mini Program settings
2. **project.config.json** - Set base library version to 3.0.0

### Step 2: Clean Rebuild Process

**Important: Follow these steps in exact order:**

1. **Close WeChat Developer Tools completely**
2. **Delete the unpackage directory**:
   ```bash
   rm -rf doctorapp/unpackage
   ```
3. **Restart WeChat Developer Tools**
4. **Recompile the project**
5. **Check base library version in WeChat Developer Tools**

### Step 3: Verify Settings in WeChat Developer Tools

1. Open **Project Settings** in WeChat Developer Tools
2. Check **Base Library Version** is set to `3.0.0` or higher
3. Ensure **ES6 to ES5** is enabled
4. Verify **Enhanced Compilation** is enabled

### Step 4: Manual Configuration Check

If the automatic fix doesn't work, manually verify these settings:

#### In manifest.json:
```json
"mp-weixin": {
  "appid": "wx5083ba6d79dec9df",
  "libVersion": "3.0.0",
  "setting": {
    "urlCheck": false,
    "es6": true,
    "postcss": false,
    "minified": false,
    "newFeature": true,
    "bigPackageSizeSupport": true
  }
}
```

#### In project.config.json:
```json
{
  "libVersion": "3.0.0",
  "setting": {
    "es6": true,
    "postcss": false,
    "minified": false,
    "newFeature": true,
    "bigPackageSizeSupport": true
  }
}
```

## 🛠️ Alternative Solutions

### Solution A: Use Compatibility Script
Run the automated fix script:
```bash
node doctorapp/scripts/fix-compatibility.js
```

### Solution B: Manual WeChat Developer Tools Settings
1. Open WeChat Developer Tools
2. Go to **Settings** → **Project Settings**
3. Set **Base Library Version** to `3.0.0`
4. Enable **ES6 to ES5 Compilation**
5. Enable **Enhanced Compilation**
6. Clear cache: **Tools** → **Clear Cache** → **Clear All Cache**

### Solution C: Downgrade Base Library (Last Resort)
If the above doesn't work, try using an older stable version:
- Set `libVersion` to `2.32.0` (known stable version)
- This may require adjusting some newer API calls

## 🔍 Common Causes

1. **Version Mismatch**: uniapp version incompatible with WeChat base library
2. **Cache Issues**: Old compiled files causing conflicts
3. **Missing Configuration**: Required settings not properly set
4. **Developer Tools Version**: Outdated WeChat Developer Tools

## 📋 Verification Checklist

After applying fixes, verify:

- [ ] App starts without errors
- [ ] No console errors related to `getStaticSystemInfoSync`
- [ ] Location API works (if testing location features)
- [ ] All pages load correctly
- [ ] Navigation between pages works

## 🆘 If Problems Persist

1. **Check uniapp Version**: Ensure you're using a compatible uniapp version
2. **Update WeChat Developer Tools**: Use the latest version
3. **Check WeChat Mini Program Documentation**: For latest compatibility requirements
4. **Consider Framework Migration**: If using very old uniapp version, consider upgrading

## 📞 Additional Resources

- [WeChat Mini Program Base Library Compatibility](https://developers.weixin.qq.com/miniprogram/dev/framework/compatibility.html)
- [uniapp WeChat Mini Program Configuration](https://uniapp.dcloud.net.cn/collocation/manifest.html#mp-weixin)
- [WeChat Developer Tools Documentation](https://developers.weixin.qq.com/miniprogram/dev/devtools/devtools.html)

---

**Last Updated**: 2025-10-28
**Status**: ✅ Fixed - Configuration updated for compatibility
