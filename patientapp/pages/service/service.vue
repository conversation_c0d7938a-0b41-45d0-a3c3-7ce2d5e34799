<template>
  <view class="service-container">
    <!-- Search Bar -->
    <view class="search-bar">
      <uni-search-bar @confirm="search" placeholder="服务" v-model="searchKeyword"></uni-search-bar>
    </view>

    <!-- Main Content -->
    <view class="main-content">
      <!-- Left Categories -->
      <view class="category-sidebar">
        <view 
          class="category-item" 
          :class="{ active: selectedCategoryId === null }"
          @click="selectCategory(null)"
        >
          <text>全部分类</text>
        </view>
        <view 
          v-for="category in categories" 
          :key="category.id"
          class="category-item"
          :class="{ active: selectedCategoryId === category.id }"
          @click="selectCategory(category.id)"
        >
          <text>{{ category.name }}</text>
        </view>
      </view>

      <!-- Right Services -->
      <view class="service-content">
        <!-- Loading -->
        <view v-if="loading" class="loading-container">
          <uni-load-more status="loading"></uni-load-more>
        </view>
        
        <!-- Services List -->
        <view v-else class="service-list">
          <view 
            v-for="service in filteredServices" 
            :key="service.id"
            class="service-item"
            @click="goToServiceDetail(service.id)"
          >
            <image :src="service.imageUrl" class="service-image" mode="aspectFill"></image>
            <view class="service-info">
              <text class="service-name">{{ service.name }}</text>
              <text class="service-description">{{ service.description }}</text>
              <view class="service-footer">
                <text class="service-price">￥{{ service.price }}/次</text>
                <button class="order-button" size="mini" @click.stop="goToServiceDetail(service.id)">去购买</button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';

// State
const categories = ref([]);
const services = ref([]);
const selectedCategoryId = ref(null);
const searchKeyword = ref('');
const loading = ref(true);

// Computed
const filteredServices = computed(() => {
  let filtered = services.value;
  
  // Filter by category
  if (selectedCategoryId.value !== null) {
    const selectedCategory = categories.value.find(c => c.id === selectedCategoryId.value);
    if (selectedCategory) {
      filtered = filtered.filter(service => service.tag === selectedCategory.name);
    }
  }
  
  // Filter by search keyword
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.trim().toLowerCase();
    filtered = filtered.filter(service => 
      service.name.toLowerCase().includes(keyword) ||
      service.description.toLowerCase().includes(keyword)
    );
  }
  
  return filtered;
});

// Lifecycle
onMounted(() => {
  fetchData();
});

// Methods
const fetchData = async () => {
  loading.value = true;
  try {
    // Fetch categories from service-tags endpoint and services separately
    const [tagsRes, servicesRes] = await Promise.all([
      uni.request({
        url: `${API_BASE_URL}/service-tags/active`,
        method: 'GET'
      }),
      uni.request({
        url: `${API_BASE_URL}/services?statusFilter=active`,
        method: 'GET'
      })
    ]);

    if (tagsRes.statusCode === 200) {
      // Convert tag names to category objects for consistency
      categories.value = (tagsRes.data || []).map((tagName, index) => ({
        id: index + 1,
        name: tagName
      }));
    }

    if (servicesRes.statusCode === 200) {
      services.value = (servicesRes.data.services || []).map(service => ({
        ...service,
        imageUrl: `${STATIC_BASE_URL}${service.image_url || '/static/service/default.png'}`
      }));
    }
  } catch (error) {
    console.error('Failed to fetch data:', error);
    uni.showToast({ title: '加载失败', icon: 'none' });
  } finally {
    loading.value = false;
  }
};

const selectCategory = (categoryId) => {
  selectedCategoryId.value = categoryId;
};

const search = (e) => {
  searchKeyword.value = e.value;
};

const goToServiceDetail = (serviceId) => {
  uni.navigateTo({
    url: `/pages/order/ordering?id=${serviceId}`
  });
};
</script>

<style scoped>
.service-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f4f4;
}

.search-bar {
  background-color: #ffffff;
  padding: 8px 10px;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.category-sidebar {
  width: 100px;
  background-color: #f8f8f8;
  border-right: 1px solid #e0e0e0;
}

.category-item {
  padding: 15px 10px;
  text-align: center;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
  color: #666;
}

.category-item.active {
  background-color: #ffffff;
  color: #007aff;
  font-weight: bold;
}

.service-content {
  flex: 1;
  background-color: #ffffff;
  overflow-y: auto;
}

.loading-container {
  padding: 50px 0;
  text-align: center;
}

.service-list {
  padding: 10px;
}

.service-item {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.service-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  margin-right: 15px;
  flex-shrink: 0;
}

.service-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.service-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.service-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 16px;
  color: #ff5050;
  font-weight: bold;
}

.order-button {
  background-color: #007aff;
  color: white;
  border-radius: 20px;
  padding: 0 12px;
  line-height: 28px;
  height: 28px;
  font-size: 13px;
}
</style>
