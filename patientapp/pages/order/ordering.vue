<template>
  <view class="detail-container" v-if="service">
    <image :src="service.imageUrl" class="service-image" mode="aspectFill"></image>

    <view class="info-section">
      <text class="service-name">{{ service.name }}</text>
      <text class="service-price">￥{{ service.price }}/次</text>
    </view>

    <view class="detail-section">
      <text class="section-title">服务详情</text>
      <rich-text :nodes="service.description" class="service-description"></rich-text>
      <view v-if="service.content" class="service-content">
        <rich-text :nodes="service.content" class="service-rich-content"></rich-text>
      </view>
    </view>

    <view class="patient-section">
      <picker @change="bindPatientChange" :value="patientIndex" :range="patients" range-key="name">
        <view class="picker">
          <text class="section-title">就诊人</text>
          <text>{{ patients.length > 0 ? patients[patientIndex].name : '请选择就诊人' }}</text>
        </view>
      </picker>
    </view>

    <view class="time-section">
      <text class="section-title">预约时间</text>
      <uni-datetime-picker 
        type="date" 
        :disable-weekends="true" 
        :value="selectedDate" 
        @change="onDateChange" 
        :start="startDate" 
        :end="endDate" 
      />
      <view class="time-slots" v-if="selectedDate">
        <text class="time-slot-title">选择时间段</text>
        <view class="slots-container">
          <button 
            v-for="(slot, index) in timeSlots" 
            :key="index" 
            class="slot-button"
            :class="{ 'selected': selectedTimeSlot === slot }"
            @click="selectTimeSlot(slot)">
            {{ slot }}
          </button>
        </view>
      </view>
    </view>

    <view class="footer-bar">
      <button 
        class="order-button" 
        @click="createOrder" 
        :disabled="isOrdering || patients.length === 0 || !selectedTimeSlot"
        :loading="isOrdering"
      >
        {{ isOrdering ? '处理中...' : '立即预约' }}
      </button>
    </view>
  </view>
  <view v-else-if="loading" class="loading-state">
    <uni-load-more status="loading"></uni-load-more>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { API_BASE_URL, STATIC_BASE_URL } from '@/utils/config.js';
import { orderActions } from '@/utils/orderActions.js'

// Use the order actions composable
const {
  detectMockPayment,
  handleMockPaymentFlow,
  handleRealPaymentFlow
} = orderActions();

const service = ref(null);
const serviceId = ref(null);
const loading = ref(true);
const isOrdering = ref(false);

const patients = ref([]);
const patientIndex = ref(0);

const selectedDate = ref('');
const selectedTimeSlot = ref('');
const timeSlots = ref([
  '09:00',
  '10:00',
  '11:00',
  '14:00',
  '15:00',
  '16:00'
]);

const getDateString = (offset) => {
  const date = new Date();
  date.setDate(date.getDate() + offset);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const startDate = computed(() => getDateString(1));
const endDate = computed(() => getDateString(14));

onLoad((options) => {
  if (options.id) {
    serviceId.value = options.id;
    fetchServiceDetails();
    fetchUserPatients();
  }
});

const fetchServiceDetails = () => {
  loading.value = true;
  uni.request({
    url: `${API_BASE_URL}/services/${serviceId.value}`,
    method: 'GET',
    success: (res) => {
      if (res.statusCode === 200) {
        // Process description and content to convert relative image URLs to absolute
        let processedDescription = res.data.description;
        let processedContent = res.data.content;
        
        if (processedDescription) {
          processedDescription = processedDescription.replace(
            /src="\/static\//g, 
            `src="${STATIC_BASE_URL}/static`
          );
        }
        
        if (processedContent) {
          processedContent = processedContent.replace(
            /src="\/static\//g, 
            `src="${STATIC_BASE_URL}/static/`
          );
        }
        
        service.value = {
          ...res.data,
          description: processedDescription,
          content: processedContent,
          imageUrl: res.data.image_url ? 
            `${STATIC_BASE_URL}${res.data.image_url}` : 
            `${STATIC_BASE_URL}/static/service/default.png`
        };
      } else {
        uni.showToast({ title: '加载服务失败', icon: 'none' });
      }
    },
    fail: () => {
      uni.showToast({ title: '网络错误', icon: 'none' });
    },
    complete: () => {
      loading.value = false;
    }
  });
};

const fetchUserPatients = () => {
  const token = uni.getStorageSync('token');
  if (!token) {
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }
  
  uni.request({
    url: `${API_BASE_URL}/patients/`,
    method: 'GET',
    header: {
      'Authorization': `Bearer ${token}`
    },
    success: (res) => {
      if (res.statusCode === 200 && res.data) {
        patients.value = res.data;
        if (res.data.length === 0) {
          uni.showModal({
            title: '提示',
            content: '您还没有添加就诊人，是否现在去添加？',
            success: (res) => {
              if (res.confirm) {
                uni.navigateTo({ url: '/pages/patient/patient' });
              }
            }
          });
        }
      }
    },
    fail: () => {
      uni.showToast({ title: '获取就诊人信息失败', icon: 'none' });
    }
  });
};

const bindPatientChange = (e) => {
  patientIndex.value = e.detail.value;
};

const onDateChange = (date) => {
  selectedDate.value = date;
  selectedTimeSlot.value = ''; // Reset time slot when date changes
};

const selectTimeSlot = (slot) => {
  selectedTimeSlot.value = slot;
};

const createOrder = async () => {
  const token = uni.getStorageSync('token');

  if (!selectedDate.value || !selectedTimeSlot.value) {
    uni.showToast({ title: '请选择服务时间', icon: 'none' });
    return;
  }

  isOrdering.value = true;
  if (!token) {
    uni.showToast({ title: '请先登录', icon: 'none' });
    uni.navigateTo({ url: '/pages/login/login' });
    return;
  }

  const selectedPatient = patients.value[patientIndex.value];

  try {
    const res = await new Promise((resolve, reject) => {
      uni.request({
        url: `${API_BASE_URL}/orders/`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        data: {
          service_id: parseInt(serviceId.value),
          patient_id: selectedPatient.id,
          service_date: selectedDate.value,
          service_time: selectedTimeSlot.value,
        },
        success: resolve,
        fail: reject
      });
    });

    if (res.statusCode === 201 && res.data.payment_params) {
      console.log('Order created successfully:', res.data);
      
      const { payment_params } = res.data;
      
      // Create order object for payment flow
      const orderForPayment = {
        id: res.data.order_id,
        order_num: res.data.order_num,
        amount: res.data.amount,
        status: 'UNPAID'
      };
      
      // Detect payment environment from backend response
      const isMockPayment = detectMockPayment(payment_params, res.data);
      
      if (isMockPayment) {
        // MOCK PAYMENT SCENARIO
        await handleMockPaymentFlow(
          res.data, 
          orderForPayment, 
          () => {
            // Navigate to order list after successful payment
            uni.switchTab({ url: '/pages/order/orderlist' });
          }
        );
      } else {
        // REAL PAYMENT SCENARIO  
        await handleRealPaymentFlow(
          payment_params, 
          orderForPayment, 
          () => {
            // Navigate to order list after successful payment
            uni.switchTab({ url: '/pages/order/orderlist' });
          },
          // Retry callback - not applicable for new orders
          null
        );
      }
      
    } else {
      // Handle order creation failure
      const errorMessage = res.data?.detail || '创建订单失败';
      uni.showToast({ title: errorMessage, icon: 'none' });
    }
  } catch (error) {
    console.error('Create order error:', error);
    uni.showToast({ title: '网络错误，请稍后重试', icon: 'none' });
  } finally {
    isOrdering.value = false;
  }
};

</script>

<style scoped>
.detail-container {
  padding-bottom: 70px; /* Space for the footer bar */
}
.service-image {
  width: 100%;
  height: 250px;
}
.info-section {
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}
.service-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}
.service-price {
  font-size: 18px;
  color: #ff5050;
  font-weight: bold;
  margin-top: 10px;
}
.detail-section, .patient-section {
  padding: 15px;
  background-color: #fff;
  margin-top: 10px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}
.service-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}
.service-rich-content {
  margin-top: 10px;
  line-height: 1.6;
  color: #333;
}

.service-content {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}
.time-section {
  padding: 15px;
  background-color: #fff;
  margin-top: 10px;
}
.time-slots {
  margin-top: 15px;
}
.time-slot-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}
.slots-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}
.slot-button {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #eee;
  font-size: 14px;
  padding: 5px 10px;
  margin: 0;
  line-height: 1.5;
}
.slot-button.selected {
  background-color: #007aff;
  color: white;
  border-color: #007aff;
}
.patient-section .picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 10px 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
}
.order-button {
  flex-grow: 1;
  background-color: #007aff;
  color: white;
}
.order-button:disabled {
  background-color: #ccc;
  color: #999;
}
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
</style>
